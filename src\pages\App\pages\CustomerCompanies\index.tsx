import { Spinner } from '@/components/loaders/spinner';
import { CustomerCompaniesTable } from '@/components/tables/customerCompanies';
import { useApi } from '@/contexts/api';
import { useAuth } from '@/contexts/auth';
import { useQuery } from '@tanstack/react-query';

export default function CustomerCompanyPage() {
  const { user } = useAuth();

  const {
    clients: { loadClientCompanies },
  } = useApi();

  const {
    data: allCompanies,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['menus', user],
    queryFn: () => loadClientCompanies(),
    retry: 1,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });

  if (isLoading || isFetching) {
    return <Spinner />;
  }

  return (
    <div className="flex flex-wrap gap-8 p-8 px-6">
      <CustomerCompaniesTable data={allCompanies?.result || []} />
    </div>
  );
}

import { Button } from '../ui/button';
import { ColumnDef, DataTable } from '../ui/data-table';

type CustomerCompaniesTableProps = {
  data: [];
};

export function CustomerCompaniesTable({ data }: CustomerCompaniesTableProps) {
  const columns: ColumnDef[] = [
    {
      id: 'name',
      header: 'Nome',
      accessorKey: 'name',
      sortable: true,
      searchable: true,
    },
    {
      id: 'isActive',
      header: 'Ativo',
      accessorKey: 'isActive',
      sortable: true,
      searchable: true,
    },
    {
      id: 'isIncluded',
      header: 'Incluso Ben + Saúde',
      accessorKey: 'isIncluded',
      sortable: true,
      searchable: true,
      cell: ({ value }) => (
        <Button variant={value === 'Admin' ? 'default' : 'secondary'}>
          {value}
        </Button>
      ),
    },
    {
      id: 'created_at',
      header: 'Data de cadastro',
      accessorKey: 'created_at',
      sortable: true,
      align: 'center',
      cell: ({ value }) => (
        <Button variant={value === 'active' ? 'default' : 'destructive'}>
          {value}
        </Button>
      ),
    },
    {
      id: 'createdAt',
      header: 'Created',
      accessorKey: 'createdAt',
      sortable: true,
      cell: ({ value }) => new Date(value).toLocaleDateString(),
    },
    {
      id: 'actions',
      header: 'Actions',
      sortable: false,
      align: 'right',
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
          >
            Edit
          </Button>
          <Button
            size="sm"
            variant="destructive"
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DataTable
      className="w-full"
      columns={columns}
      fetchData={fetchData}
      initialPageSize={10}
      pageSizeOptions={[10, 25, 50]}
      enableSearch
      enableSorting
      enablePagination
      searchPlaceholder="Pesquise pelo nome da empresa..."
      queryKey={['all-companies']}
      searchDebounceMs={300}
    />
  );
}
